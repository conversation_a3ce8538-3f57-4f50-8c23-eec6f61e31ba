import { logger } from '@utils/logger';
import { Platform } from 'react-native';

export interface NotificationSoundConfig {
  soundName: string;
  enabled: boolean;
}

export class NotificationSoundService {
  private static defaultSoundName = 'notification_158187';
  private static currentSoundConfig: NotificationSoundConfig = {
    soundName: 'notification_158187',
    enabled: true,
  };

  /**
   * Get the current notification sound configuration
   */
  public static getCurrentSoundConfig(): NotificationSoundConfig {
    return { ...this.currentSoundConfig };
  }

  /**
   * Set a custom notification sound
   * @param soundName - Name of the sound file (without extension)
   * @param enabled - Whether custom sound is enabled
   */
  public static setCustomSound(
    soundName: string,
    enabled: boolean = true,
  ): void {
    try {
      this.currentSoundConfig = {
        soundName: soundName || this.defaultSoundName,
        enabled,
      };

      logger.info('Custom notification sound configured:', {
        soundName: this.currentSoundConfig.soundName,
        enabled: this.currentSoundConfig.enabled,
        platform: Platform.OS,
      });
    } catch (error) {
      logger.error('Error setting custom notification sound:', error);
    }
  }

  /**
   * Reset to default notification sound
   */
  public static resetToDefault(): void {
    this.setCustomSound(this.defaultSoundName, true);
    logger.info('Notification sound reset to default');
  }

  /**
   * Disable custom notification sound (use system default)
   */
  public static disableCustomSound(): void {
    this.currentSoundConfig.enabled = false;
    logger.info('Custom notification sound disabled');
  }

  /**
   * Enable custom notification sound
   */
  public static enableCustomSound(): void {
    this.currentSoundConfig.enabled = true;
    logger.info('Custom notification sound enabled');
  }

  /**
   * Get the sound name to use for notifications
   * Returns null if custom sounds are disabled (use system default)
   */
  public static getSoundNameForNotification(): string | null {
    if (!this.currentSoundConfig.enabled) {
      return null; // Use system default
    }
    return this.currentSoundConfig.soundName;
  }

  /**
   * Validate if a sound file exists (platform-specific)
   * Note: This is a basic validation - actual file existence should be verified during development
   */
  public static validateSoundName(soundName: string): boolean {
    if (!soundName || soundName.trim().length === 0) {
      logger.warn('Invalid sound name provided');
      return false;
    }

    // Basic validation for sound name format
    const validNamePattern = /^[a-zA-Z0-9_-]+$/;
    if (!validNamePattern.test(soundName)) {
      logger.warn(
        'Sound name contains invalid characters. Use only letters, numbers, hyphens, and underscores.',
      );
      return false;
    }

    return true;
  }

  /**
   * Get platform-specific sound file requirements
   */
  public static getSoundFileRequirements(): {
    platform: string;
    supportedFormats: string[];
    recommendedFormat: string;
    maxSize: string;
    location: string;
  } {
    if (Platform.OS === 'ios') {
      return {
        platform: 'iOS',
        supportedFormats: ['caf', 'wav', 'aiff', 'mp3'],
        recommendedFormat: 'caf',
        maxSize: '1MB',
        location: 'app/ios/bppulse/Sounds/',
      };
    } else {
      return {
        platform: 'Android',
        supportedFormats: ['mp3', 'wav', 'ogg'],
        recommendedFormat: 'mp3',
        maxSize: '1MB',
        location: 'app/android/app/src/main/res/raw/',
      };
    }
  }

  /**
   * Log current configuration for debugging
   */
  public static logCurrentConfiguration(): void {
    const config = this.getCurrentSoundConfig();
    const requirements = this.getSoundFileRequirements();

    logger.info('Notification Sound Service Configuration:', {
      currentSound: config.soundName,
      enabled: config.enabled,
      platform: requirements.platform,
      soundFileLocation: requirements.location,
      recommendedFormat: requirements.recommendedFormat,
    });
  }
}
