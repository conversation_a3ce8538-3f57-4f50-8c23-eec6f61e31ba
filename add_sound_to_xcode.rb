#!/usr/bin/env ruby

require 'xcodeproj'

# Path to the Xcode project
project_path = 'app/ios/bppulse.xcodeproj'
sound_file_path = 'Sounds/notification_158187.caf'

# Open the project
project = Xcodeproj::Project.open(project_path)

# Find the main target
target = project.targets.find { |t| t.name == 'bppulse' }

if target.nil?
  puts "Error: Could not find target 'bppulse'"
  exit 1
end

# Find the main group
main_group = project.main_group

# Create or find the Sounds group
sounds_group = main_group.groups.find { |g| g.name == 'Sounds' }
if sounds_group.nil?
  sounds_group = main_group.new_group('Sounds')
end

# Add the sound file to the project
file_ref = sounds_group.new_reference(sound_file_path)
file_ref.name = 'notification_158187.caf'

# Add the file to the target's resources build phase
resources_build_phase = target.build_phases.find { |bp| bp.is_a?(Xcodeproj::Project::Object::PBXResourcesBuildPhase) }
if resources_build_phase.nil?
  resources_build_phase = target.new_resources_build_phase
end

# Add the file to the build phase
build_file = resources_build_phase.add_file_reference(file_ref)

# Save the project
project.save

puts "Successfully added notification_158187.caf to Xcode project"
